{"__meta": {"id": "01K7558229JA6BGM92RPP0S7SS", "datetime": "2025-10-09 19:55:28", "utime": ********28.841706, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[19:55:27] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php on line 102", "message_html": null, "is_string": false, "label": "warning", "time": ********27.561931, "xdebug_link": null, "collector": "log"}, {"message": "[19:55:27] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php on line 102", "message_html": null, "is_string": false, "label": "warning", "time": ********27.830211, "xdebug_link": null, "collector": "log"}, {"message": "[19:55:28] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": ********28.822158, "xdebug_link": null, "collector": "log"}, {"message": "[19:55:28] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": ********28.822873, "xdebug_link": null, "collector": "log"}]}, "time": {"start": ********25.453351, "end": ********28.841738, "duration": 3.3883869647979736, "duration_str": "3.39s", "measures": [{"label": "Booting", "start": ********25.453351, "relative_start": 0, "end": ********25.907727, "relative_end": ********25.907727, "duration": 0.****************, "duration_str": "454ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": ********25.907749, "relative_start": 0.*****************, "end": ********28.841741, "relative_end": 3.0994415283203125e-06, "duration": 2.****************, "duration_str": "2.93s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": ********25.918635, "relative_start": 0.****************, "end": ********25.920185, "relative_end": ********25.920185, "duration": 0.0015501976013183594, "duration_str": "1.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.521742, "relative_start": 1.****************, "end": **********.521742, "relative_end": **********.521742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.538629, "relative_start": 1.***************, "end": **********.538629, "relative_end": **********.538629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.551356, "relative_start": 1.0980050563812256, "end": **********.551356, "relative_end": **********.551356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.567541, "relative_start": 1.114189863204956, "end": **********.567541, "relative_end": **********.567541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.573485, "relative_start": 1.120133876800537, "end": **********.573485, "relative_end": **********.573485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": ********28.819058, "relative_start": 3.3657069206237793, "end": ********28.819058, "relative_end": ********28.819058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": ********28.820287, "relative_start": 3.366935968399048, "end": ********28.820287, "relative_end": ********28.820287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********28.829663, "relative_start": 3.376312017440796, "end": ********28.829663, "relative_end": ********28.829663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": ********28.838899, "relative_start": 3.3855478763580322, "end": ********28.841352, "relative_end": ********28.841352, "duration": 0.0024530887603759766, "duration_str": "2.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8802976, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.521665, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.53855, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.551267, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.567336, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.573411, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": ********28.81892, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": ********28.820162, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": ********28.829518, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 501, "nb_statements": 831, "nb_visible_statements": 501, "nb_excluded_statements": 331, "nb_failed_statements": 0, "accumulated_duration": 0.9676800000000007, "accumulated_duration_str": "968ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": ********25.930523, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'xd1Tny3ZEeEN1t1yUHLLVdclXpUXxe7Qd1vuAoXN' limit 1", "type": "query", "params": [], "bindings": ["xd1Tny3ZEeEN1t1yUHLLVdclXpUXxe7Qd1vuAoXN"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": ********25.932709, "duration": 0.40984, "duration_str": "410ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 42.353}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.3665788, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 42.353, "width_percent": 0.133}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.412548, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 42.486, "width_percent": 0.074}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.416374, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 42.561, "width_percent": 0.07}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-10-09' and date(`semester_end`) >= '2025-10-09' limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-09", "2025-10-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.421899, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 42.631, "width_percent": 0.087}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '3' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 193}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.435245, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:193", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=193", "ajax": false, "filename": "OverviewResource.php", "line": "193"}, "connection": "racoed", "explain": null, "start_percent": 42.718, "width_percent": 0.12}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 199}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.440096, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:199", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=199", "ajax": false, "filename": "OverviewResource.php", "line": "199"}, "connection": "racoed", "explain": null, "start_percent": 42.838, "width_percent": 0.071}, {"sql": "select * from `levels` where `levels`.`id` = '2' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 205}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.44552, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:205", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 205}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=205", "ajax": false, "filename": "OverviewResource.php", "line": "205"}, "connection": "racoed", "explain": null, "start_percent": 42.909, "width_percent": 0.073}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.4492989, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:211", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=211", "ajax": false, "filename": "OverviewResource.php", "line": "211"}, "connection": "racoed", "explain": null, "start_percent": 42.982, "width_percent": 0.078}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 598}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 576}, {"index": 13, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 165}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.456979, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:598", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 598}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=598", "ajax": false, "filename": "OverviewResource.php", "line": "598"}, "connection": "racoed", "explain": null, "start_percent": 43.06, "width_percent": 0.238}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 610}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 576}, {"index": 13, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 165}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.4644809, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:610", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 610}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=610", "ajax": false, "filename": "OverviewResource.php", "line": "610"}, "connection": "racoed", "explain": null, "start_percent": 43.297, "width_percent": 0.076}, {"sql": "select exists(select * from `courses` where (`level_id` = '2' and `semester_id` = '1' and `department_id` = '16') order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC) as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 587}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 165}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.469304, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:587", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 587}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=587", "ajax": false, "filename": "OverviewResource.php", "line": "587"}, "connection": "racoed", "explain": null, "start_percent": 43.374, "width_percent": 0.253}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 172}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.47498, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:172", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=172", "ajax": false, "filename": "OverviewResource.php", "line": "172"}, "connection": "racoed", "explain": null, "start_percent": 43.627, "width_percent": 0.07}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "2", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.4807148, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "racoed", "explain": null, "start_percent": 43.697, "width_percent": 0.172}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null order by `last_name` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "2", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.486082, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 43.869, "width_percent": 0.469}, {"sql": "select * from `registrations` where `registrations`.`user_id` in (15, 16, 17, 18, 19, 20, 21, 22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.493983, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 44.338, "width_percent": 0.112}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 130}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.513696, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:130", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=130", "ajax": false, "filename": "OverviewResource.php", "line": "130"}, "connection": "racoed", "explain": null, "start_percent": 44.45, "width_percent": 0.07}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 140}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.531801, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:140", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=140", "ajax": false, "filename": "OverviewResource.php", "line": "140"}, "connection": "racoed", "explain": null, "start_percent": 44.52, "width_percent": 0.07}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 150}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.544812, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:150", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=150", "ajax": false, "filename": "OverviewResource.php", "line": "150"}, "connection": "racoed", "explain": null, "start_percent": 44.59, "width_percent": 0.069}, {"sql": "select `name`, `id` from `departments` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 160}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.560486, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:160", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=160", "ajax": false, "filename": "OverviewResource.php", "line": "160"}, "connection": "racoed", "explain": null, "start_percent": 44.659, "width_percent": 0.084}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '2' and `semester_id` = '1' order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": ["16", "2", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 234}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeCopied.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeCopied.php", "line": 18}], "start": **********.60741, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:234", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=234", "ajax": false, "filename": "OverviewResource.php", "line": "234"}, "connection": "racoed", "explain": null, "start_percent": 44.743, "width_percent": 0.154}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.611957, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 44.897, "width_percent": 0.089}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.615405, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 44.986, "width_percent": 0.103}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 244}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 337}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.6205091, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:244", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=244", "ajax": false, "filename": "OverviewResource.php", "line": "244"}, "connection": "racoed", "explain": null, "start_percent": 45.089, "width_percent": 0.07}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.623822, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 45.16, "width_percent": 0.092}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.6274428, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 45.252, "width_percent": 0.096}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.630982, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 45.348, "width_percent": 0.096}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.634593, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 45.444, "width_percent": 0.088}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.638467, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 45.532, "width_percent": 0.074}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.6416872, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 45.606, "width_percent": 0.085}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.64461, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 45.691, "width_percent": 0.089}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.648414, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 45.78, "width_percent": 0.092}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.652579, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 45.872, "width_percent": 0.147}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.657743, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 46.018, "width_percent": 0.124}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.663689, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 46.142, "width_percent": 0.113}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.6709042, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 46.255, "width_percent": 0.101}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '2' and `semester_id` = '1' order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": ["16", "2", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 234}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeCopied.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeCopied.php", "line": 18}], "start": **********.676311, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:234", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=234", "ajax": false, "filename": "OverviewResource.php", "line": "234"}, "connection": "racoed", "explain": null, "start_percent": 46.356, "width_percent": 0.153}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.683128, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 46.509, "width_percent": 0.103}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.689043, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 46.613, "width_percent": 0.197}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.694288, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 46.81, "width_percent": 0.086}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.698205, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 46.896, "width_percent": 0.087}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.7018712, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 46.982, "width_percent": 0.085}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.704764, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 47.067, "width_percent": 0.094}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.709249, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 47.161, "width_percent": 0.116}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.7152328, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 47.277, "width_percent": 0.114}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.722102, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 47.391, "width_percent": 0.26}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.727827, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 47.651, "width_percent": 0.119}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.733198, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 47.77, "width_percent": 0.105}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.739305, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 47.875, "width_percent": 0.113}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.74502, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 47.988, "width_percent": 0.096}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.750968, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 48.084, "width_percent": 0.169}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '2' and `semester_id` = '1' order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": ["16", "2", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 234}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 19, "namespace": "view", "name": "filament-tables::columns.text-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-column.blade.php", "line": 23}], "start": **********.7577949, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:234", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=234", "ajax": false, "filename": "OverviewResource.php", "line": "234"}, "connection": "racoed", "explain": null, "start_percent": 48.254, "width_percent": 0.15}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.76253, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 48.403, "width_percent": 0.091}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.766296, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 48.494, "width_percent": 0.093}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.771132, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 48.587, "width_percent": 0.109}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.776109, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 48.696, "width_percent": 0.107}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.781081, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 48.803, "width_percent": 0.095}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.786527, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 48.898, "width_percent": 0.151}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.7927492, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 49.049, "width_percent": 0.123}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.79865, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 49.172, "width_percent": 0.125}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.803964, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 49.297, "width_percent": 0.115}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8096201, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 49.412, "width_percent": 0.12}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.815176, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 49.532, "width_percent": 0.111}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.821289, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 49.642, "width_percent": 0.126}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}], "start": **********.827244, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 49.769, "width_percent": 0.122}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 366}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.833162, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 49.89, "width_percent": 0.125}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 258}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 98}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeCopied.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeCopied.php", "line": 18}], "start": **********.839784, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:258", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 258}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=258", "ajax": false, "filename": "OverviewResource.php", "line": "258"}, "connection": "racoed", "explain": null, "start_percent": 50.016, "width_percent": 0.22}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.8502638, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 50.236, "width_percent": 0.131}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.856917, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 50.367, "width_percent": 0.129}, {"sql": "select `max_score` from `grades` where `min_score` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 375}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8630018, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:278", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=278", "ajax": false, "filename": "OverviewResource.php", "line": "278"}, "connection": "racoed", "explain": null, "start_percent": 50.496, "width_percent": 0.096}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.868473, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 50.592, "width_percent": 0.119}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.874227, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 50.711, "width_percent": 0.105}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.880475, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 50.816, "width_percent": 0.126}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.88729, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 50.942, "width_percent": 0.153}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.893904, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 51.095, "width_percent": 0.121}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.899741, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 51.216, "width_percent": 0.091}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.9051142, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 51.307, "width_percent": 0.121}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.910835, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 51.428, "width_percent": 0.121}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.916967, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 51.549, "width_percent": 0.115}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.923358, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 51.664, "width_percent": 0.141}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.9293191, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 51.804, "width_percent": 0.12}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.935982, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 51.924, "width_percent": 0.112}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.941861, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 52.036, "width_percent": 0.092}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.946706, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 52.128, "width_percent": 0.089}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.950971, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 52.217, "width_percent": 0.167}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.9560878, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 52.384, "width_percent": 0.099}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.960133, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 52.483, "width_percent": 0.092}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.964418, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 52.575, "width_percent": 0.143}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.970478, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 52.718, "width_percent": 0.105}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.974869, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 52.823, "width_percent": 0.12}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.979867, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 52.943, "width_percent": 0.072}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.983667, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 53.015, "width_percent": 0.146}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.9886858, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 53.161, "width_percent": 0.096}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.992631, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 53.257, "width_percent": 0.111}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": **********.998696, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 53.368, "width_percent": 0.12}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": ********27.0041938, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 53.488, "width_percent": 0.118}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": ********27.010089, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 53.606, "width_percent": 0.114}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": ********27.014439, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 53.719, "width_percent": 0.1}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 326}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}], "start": ********27.020115, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:272", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=272", "ajax": false, "filename": "OverviewResource.php", "line": "272"}, "connection": "racoed", "explain": null, "start_percent": 53.819, "width_percent": 0.208}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 373}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": ********27.0253801, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:335", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 335}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=335", "ajax": false, "filename": "OverviewResource.php", "line": "335"}, "connection": "racoed", "explain": null, "start_percent": 54.027, "width_percent": 0.102}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.029995, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.129, "width_percent": 0.115}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.032529, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.244, "width_percent": 0.104}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.034864, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.349, "width_percent": 0.098}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.037014, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.447, "width_percent": 0.111}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.0395708, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.557, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.0417771, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.654, "width_percent": 0.101}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.044214, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.756, "width_percent": 0.126}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.046725, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.882, "width_percent": 0.143}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.049449, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.024, "width_percent": 0.109}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.052043, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.133, "width_percent": 0.128}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.0555248, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.261, "width_percent": 0.095}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.057293, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.356, "width_percent": 0.089}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.059118, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.445, "width_percent": 0.084}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.060775, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.529, "width_percent": 0.087}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.0625482, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.615, "width_percent": 0.083}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.064208, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.698, "width_percent": 0.088}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.066015, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.786, "width_percent": 0.083}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.067775, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.869, "width_percent": 0.103}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.069643, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.972, "width_percent": 0.119}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.071888, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.091, "width_percent": 0.133}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.074863, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.224, "width_percent": 0.112}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.0773149, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.336, "width_percent": 0.118}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.080023, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.454, "width_percent": 0.103}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.082366, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.557, "width_percent": 0.114}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.100867, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.671, "width_percent": 0.2}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.104745, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.871, "width_percent": 0.114}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1071858, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.985, "width_percent": 0.113}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1098218, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.097, "width_percent": 0.245}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.113501, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.342, "width_percent": 0.073}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.115797, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.416, "width_percent": 0.087}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.118013, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.502, "width_percent": 0.129}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1206641, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.632, "width_percent": 0.141}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1231098, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.772, "width_percent": 0.097}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.125164, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.869, "width_percent": 0.103}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.127493, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.973, "width_percent": 0.118}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1297581, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.09, "width_percent": 0.103}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.131654, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.194, "width_percent": 0.102}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.133718, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.296, "width_percent": 0.114}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.135675, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.41, "width_percent": 0.096}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.137888, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.506, "width_percent": 0.202}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.141198, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.707, "width_percent": 0.09}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.143149, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.797, "width_percent": 0.099}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.145452, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.897, "width_percent": 0.096}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.147643, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.993, "width_percent": 0.104}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.15022, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.097, "width_percent": 0.132}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.152728, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.229, "width_percent": 0.219}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.156121, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.448, "width_percent": 0.101}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.158338, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.55, "width_percent": 0.119}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.160953, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.668, "width_percent": 0.101}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.163097, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.77, "width_percent": 0.126}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1653001, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.896, "width_percent": 0.086}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.167189, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.982, "width_percent": 0.117}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.169616, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.098, "width_percent": 0.11}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.171757, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.208, "width_percent": 0.103}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.175103, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.311, "width_percent": 0.148}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.178147, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.459, "width_percent": 0.106}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.180145, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.565, "width_percent": 0.095}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1821148, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.661, "width_percent": 0.082}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.183924, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.742, "width_percent": 0.092}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.185936, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.834, "width_percent": 0.063}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1874208, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.897, "width_percent": 0.081}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.1892018, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.978, "width_percent": 0.08}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.190995, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.057, "width_percent": 0.091}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.192855, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.148, "width_percent": 0.079}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.194494, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.227, "width_percent": 0.088}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.196274, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.315, "width_percent": 0.065}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.197816, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.38, "width_percent": 0.075}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.199373, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.455, "width_percent": 0.067}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.200776, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.522, "width_percent": 0.107}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.205163, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.63, "width_percent": 0.074}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.206752, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.704, "width_percent": 0.1}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.208688, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.805, "width_percent": 0.094}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.210578, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.899, "width_percent": 0.092}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.213072, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.991, "width_percent": 0.103}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2154071, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.094, "width_percent": 0.1}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.21768, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.194, "width_percent": 0.086}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.219632, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.28, "width_percent": 0.126}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.221817, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.406, "width_percent": 0.073}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.22372, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.479, "width_percent": 0.106}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.226361, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.586, "width_percent": 0.114}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2287178, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.699, "width_percent": 0.134}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.231447, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.834, "width_percent": 0.11}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.233757, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.943, "width_percent": 0.09}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2359872, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.033, "width_percent": 0.102}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2380419, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.136, "width_percent": 0.095}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2403748, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.231, "width_percent": 0.082}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.242539, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.312, "width_percent": 0.11}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.24484, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.422, "width_percent": 0.076}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.246716, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.498, "width_percent": 0.1}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2491379, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.599, "width_percent": 0.113}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.251732, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.711, "width_percent": 0.122}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.254317, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.833, "width_percent": 0.167}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.257158, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64, "width_percent": 0.111}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.259625, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.111, "width_percent": 0.072}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2613769, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.183, "width_percent": 0.098}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2633219, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.282, "width_percent": 0.075}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.265034, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.357, "width_percent": 0.085}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.267446, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.442, "width_percent": 0.105}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.269572, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.547, "width_percent": 0.101}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.271882, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.648, "width_percent": 0.094}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.274088, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.742, "width_percent": 0.116}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.27675, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.858, "width_percent": 0.125}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.279161, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.983, "width_percent": 0.113}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2815619, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.096, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2838178, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.193, "width_percent": 0.118}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.286553, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.311, "width_percent": 0.146}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2890499, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.457, "width_percent": 0.117}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.291531, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.573, "width_percent": 0.102}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.293708, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.676, "width_percent": 0.107}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.296135, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.783, "width_percent": 0.104}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.2986112, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.887, "width_percent": 0.14}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.302192, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.027, "width_percent": 0.092}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.304258, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.119, "width_percent": 0.106}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.306628, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.225, "width_percent": 0.106}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.308811, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.332, "width_percent": 0.106}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.311207, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.438, "width_percent": 0.103}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.313399, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.542, "width_percent": 0.11}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.315794, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.651, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.318229, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.748, "width_percent": 0.133}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.3212729, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.882, "width_percent": 0.098}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.323457, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.98, "width_percent": 0.132}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.3260992, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.112, "width_percent": 0.102}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.3282561, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.214, "width_percent": 0.107}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.330636, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.322, "width_percent": 0.096}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.332738, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.418, "width_percent": 0.11}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.349797, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.527, "width_percent": 0.156}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.353213, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.684, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.355618, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.844, "width_percent": 0.098}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.3575351, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.942, "width_percent": 0.085}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.359443, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.027, "width_percent": 0.105}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.361978, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.132, "width_percent": 0.1}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.364303, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.232, "width_percent": 0.103}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.3668811, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.336, "width_percent": 0.087}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.3689961, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.422, "width_percent": 0.106}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.371402, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.529, "width_percent": 0.125}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.373842, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.654, "width_percent": 0.127}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.376462, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.781, "width_percent": 0.098}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.378525, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.879, "width_percent": 0.104}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.380526, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.984, "width_percent": 0.093}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.382482, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.077, "width_percent": 0.119}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.385137, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.195, "width_percent": 0.252}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.388584, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.448, "width_percent": 0.058}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.38999, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.505, "width_percent": 0.087}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.391778, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.592, "width_percent": 0.08}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.393837, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.672, "width_percent": 0.125}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.396545, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.797, "width_percent": 0.113}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.398903, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.909, "width_percent": 0.115}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.401319, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.024, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4031072, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.121, "width_percent": 0.084}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.404821, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.205, "width_percent": 0.078}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.406364, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.283, "width_percent": 0.082}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.408129, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.364, "width_percent": 0.094}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.410181, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.458, "width_percent": 0.092}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.41238, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.55, "width_percent": 0.094}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.414378, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.644, "width_percent": 0.094}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.418566, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.738, "width_percent": 0.269}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.423255, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.007, "width_percent": 0.118}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4258559, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.125, "width_percent": 0.13}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4283218, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.255, "width_percent": 0.094}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.430092, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.349, "width_percent": 0.088}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.431866, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.437, "width_percent": 0.082}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.433733, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.518, "width_percent": 0.099}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.435772, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.618, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.437737, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.715, "width_percent": 0.112}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.440136, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.826, "width_percent": 0.111}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.442381, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.937, "width_percent": 0.107}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4447238, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.044, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.446791, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.142, "width_percent": 0.109}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.44931, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.25, "width_percent": 0.12}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.452029, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.37, "width_percent": 0.149}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.457125, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.519, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4589171, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.616, "width_percent": 0.089}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4607272, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.705, "width_percent": 0.083}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.462366, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.787, "width_percent": 0.091}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.464164, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.878, "width_percent": 0.086}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.465843, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.964, "width_percent": 0.088}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.467726, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.052, "width_percent": 0.11}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.469856, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.162, "width_percent": 0.103}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4719129, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.265, "width_percent": 0.078}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4735749, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.342, "width_percent": 0.081}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4754238, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.423, "width_percent": 0.082}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4772391, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.505, "width_percent": 0.107}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4793239, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.612, "width_percent": 0.098}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.481606, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.71, "width_percent": 0.113}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.484534, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.823, "width_percent": 0.18}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.4875388, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.003, "width_percent": 0.115}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.49004, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.117, "width_percent": 0.127}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.492637, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.245, "width_percent": 0.134}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.495489, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.379, "width_percent": 0.134}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.498259, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.513, "width_percent": 0.137}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.501014, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.651, "width_percent": 0.156}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.503922, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.807, "width_percent": 0.14}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.506874, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.946, "width_percent": 0.127}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.509521, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.073, "width_percent": 0.145}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.512319, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.218, "width_percent": 0.111}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5146518, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.329, "width_percent": 0.135}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.517648, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.464, "width_percent": 0.138}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5207078, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.602, "width_percent": 0.149}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5241492, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.751, "width_percent": 0.119}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.526581, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.87, "width_percent": 0.111}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5293422, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.981, "width_percent": 0.137}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5319421, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.118, "width_percent": 0.136}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.535243, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.255, "width_percent": 0.112}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.537653, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.366, "width_percent": 0.133}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.540666, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.499, "width_percent": 0.123}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5432801, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.622, "width_percent": 0.14}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.546285, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.762, "width_percent": 0.125}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5488732, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.887, "width_percent": 0.137}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.551779, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.024, "width_percent": 0.095}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.55374, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.12, "width_percent": 0.14}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.556361, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.259, "width_percent": 0.091}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.558521, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.35, "width_percent": 0.138}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5641658, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.488, "width_percent": 0.132}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.566759, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.621, "width_percent": 0.144}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.569596, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.764, "width_percent": 0.13}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.572181, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.895, "width_percent": 0.101}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.574781, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.996, "width_percent": 0.116}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.577172, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.112, "width_percent": 0.109}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.579761, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.22, "width_percent": 0.101}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.581985, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.321, "width_percent": 0.107}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.584491, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.429, "width_percent": 0.109}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5869238, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.537, "width_percent": 0.15}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5897288, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.687, "width_percent": 0.096}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.5918539, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.783, "width_percent": 0.097}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.59438, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.88, "width_percent": 0.107}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.596606, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.988, "width_percent": 0.112}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.614497, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.099, "width_percent": 0.221}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.6189308, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.321, "width_percent": 0.134}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.621748, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.455, "width_percent": 0.125}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.624548, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.58, "width_percent": 0.131}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.6272209, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.711, "width_percent": 0.134}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.630204, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.846, "width_percent": 0.115}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.632643, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.96, "width_percent": 0.104}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.635407, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.065, "width_percent": 0.106}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.637861, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.171, "width_percent": 0.109}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.640507, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.28, "width_percent": 0.12}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.642874, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.4, "width_percent": 0.095}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.645441, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.495, "width_percent": 0.093}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.647659, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.588, "width_percent": 0.125}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.650403, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.713, "width_percent": 0.119}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.65303, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.831, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.656437, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.011, "width_percent": 0.234}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.660749, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.245, "width_percent": 0.131}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.663038, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.376, "width_percent": 0.124}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.665689, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.5, "width_percent": 0.141}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.668269, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.641, "width_percent": 0.118}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.670764, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.758, "width_percent": 0.114}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.673202, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.872, "width_percent": 0.127}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.676121, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.999, "width_percent": 0.109}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.678109, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.108, "width_percent": 0.082}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.679799, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.189, "width_percent": 0.068}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.68124, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.258, "width_percent": 0.072}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.682795, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.33, "width_percent": 0.071}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.684253, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.401, "width_percent": 0.096}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.686286, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.497, "width_percent": 0.079}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.688324, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.576, "width_percent": 0.116}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.691112, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.692, "width_percent": 0.144}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.693562, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.835, "width_percent": 0.411}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.698304, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.247, "width_percent": 0.073}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.699861, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.32, "width_percent": 0.065}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.701359, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.385, "width_percent": 0.079}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.702982, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.464, "width_percent": 0.06}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.704349, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.523, "width_percent": 0.075}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.70594, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.599, "width_percent": 0.068}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.707412, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.667, "width_percent": 0.08}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.708997, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.747, "width_percent": 0.057}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.710237, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.804, "width_percent": 0.064}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.711664, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.868, "width_percent": 0.053}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.712896, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.92, "width_percent": 0.066}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.714635, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.986, "width_percent": 0.08}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.716667, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.066, "width_percent": 0.129}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7218971, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.195, "width_percent": 0.106}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.723749, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.302, "width_percent": 0.08}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.725403, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.381, "width_percent": 0.068}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7274241, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.449, "width_percent": 0.121}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7300851, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.57, "width_percent": 0.119}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.732544, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.689, "width_percent": 0.118}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7353618, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.807, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7374752, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.904, "width_percent": 0.104}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.73997, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.008, "width_percent": 0.114}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.742402, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.122, "width_percent": 0.126}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7451131, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.248, "width_percent": 0.121}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.747528, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.369, "width_percent": 0.128}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7503471, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.497, "width_percent": 0.143}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.753149, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.64, "width_percent": 0.145}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7567852, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.785, "width_percent": 0.121}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.759374, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.905, "width_percent": 0.343}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7645102, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.249, "width_percent": 0.177}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.767411, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.425, "width_percent": 0.117}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.769799, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.542, "width_percent": 0.102}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7721539, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.644, "width_percent": 0.116}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.774894, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.76, "width_percent": 0.126}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.777497, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.886, "width_percent": 0.101}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7800329, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.987, "width_percent": 0.113}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.782526, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.1, "width_percent": 0.125}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.785755, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.225, "width_percent": 0.128}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.788699, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.353, "width_percent": 0.129}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.791774, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.482, "width_percent": 0.124}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.794383, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.606, "width_percent": 0.124}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.7979202, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.73, "width_percent": 0.136}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.8006601, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.867, "width_percent": 0.137}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.803319, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.004, "width_percent": 0.099}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.805218, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.104, "width_percent": 0.101}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.807317, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.205, "width_percent": 0.085}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.809124, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.29, "width_percent": 0.092}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.8112109, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.381, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.813585, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.479, "width_percent": 0.125}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.816237, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.604, "width_percent": 0.107}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.818683, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.711, "width_percent": 0.134}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.8214102, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.845, "width_percent": 0.119}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.8236141, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.964, "width_percent": 0.123}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.826234, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.087, "width_percent": 0.1}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.828257, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.188, "width_percent": 0.105}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.832429, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.293, "width_percent": 0.112}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.834591, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.405, "width_percent": 0.097}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.836662, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.502, "width_percent": 0.13}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.839218, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.632, "width_percent": 0.126}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.8420742, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.758, "width_percent": 0.123}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.844664, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.881, "width_percent": 0.138}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.847766, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.019, "width_percent": 0.11}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.850136, "duration": 0.0087, "duration_str": "8.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.129, "width_percent": 0.899}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.860482, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.028, "width_percent": 0.143}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.863441, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.171, "width_percent": 0.14}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.866575, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.31, "width_percent": 0.124}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.8688521, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.434, "width_percent": 0.102}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.871229, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.536, "width_percent": 0.111}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.873539, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.647, "width_percent": 0.127}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.887764, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.774, "width_percent": 0.151}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.8904312, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.925, "width_percent": 0.086}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.892145, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.011, "width_percent": 0.081}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.893931, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.091, "width_percent": 0.07}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.895426, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.162, "width_percent": 0.076}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.8970861, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.238, "width_percent": 0.068}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.89859, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.306, "width_percent": 0.071}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.9003239, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.378, "width_percent": 0.082}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.901984, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.459, "width_percent": 0.087}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.903727, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.546, "width_percent": 0.073}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.905341, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.619, "width_percent": 0.096}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.907355, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.716, "width_percent": 0.104}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.909294, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.82, "width_percent": 0.092}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.911262, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.912, "width_percent": 0.095}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.913118, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.007, "width_percent": 0.096}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.915255, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.103, "width_percent": 0.164}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.91839, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.267, "width_percent": 0.088}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.920156, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.355, "width_percent": 0.083}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.92226, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.438, "width_percent": 0.084}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.923993, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.522, "width_percent": 0.102}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.92596, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.624, "width_percent": 0.112}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.927896, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.736, "width_percent": 0.096}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.929822, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.832, "width_percent": 0.081}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.931464, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.912, "width_percent": 0.086}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.9336, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.998, "width_percent": 0.125}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.936168, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.123, "width_percent": 0.146}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.939089, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.269, "width_percent": 0.109}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.941413, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.377, "width_percent": 0.134}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.944019, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.512, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.946691, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.659, "width_percent": 0.138}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.950297, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.798, "width_percent": 0.228}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.953998, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.026, "width_percent": 0.105}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.956179, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.132, "width_percent": 0.127}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.958832, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.259, "width_percent": 0.11}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.961067, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.368, "width_percent": 0.109}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.96361, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.477, "width_percent": 0.109}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.966851, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.585, "width_percent": 0.12}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.969321, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.705, "width_percent": 0.11}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.971868, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.815, "width_percent": 0.173}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.974813, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.987, "width_percent": 0.109}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.976835, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.096, "width_percent": 0.096}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.978874, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.192, "width_percent": 0.086}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.980644, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.278, "width_percent": 0.117}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.982892, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.394, "width_percent": 0.094}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.985096, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.489, "width_percent": 0.094}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.989735, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.583, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.992996, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.751, "width_percent": 0.157}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.996357, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.908, "width_percent": 0.122}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********27.998788, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.03, "width_percent": 0.119}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.0019288, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.149, "width_percent": 0.118}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.0045621, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.267, "width_percent": 0.136}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.007319, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.403, "width_percent": 0.146}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.010286, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.549, "width_percent": 0.145}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.013321, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.693, "width_percent": 0.131}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.015968, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.825, "width_percent": 0.197}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.019132, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.022, "width_percent": 0.111}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.022163, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.133, "width_percent": 0.135}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.024915, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.268, "width_percent": 0.105}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.027072, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.373, "width_percent": 0.109}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.02957, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.482, "width_percent": 0.091}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.03158, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.573, "width_percent": 0.098}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.034155, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.671, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.0365162, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.768, "width_percent": 0.136}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.0392878, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.905, "width_percent": 0.135}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.041936, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.04, "width_percent": 0.112}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.044336, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.152, "width_percent": 0.125}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.046675, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.277, "width_percent": 0.105}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.0489838, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.382, "width_percent": 0.097}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.051025, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.479, "width_percent": 0.115}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.053459, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.594, "width_percent": 0.105}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.0554721, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.699, "width_percent": 0.105}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.057533, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.805, "width_percent": 0.093}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********28.059467, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.898, "width_percent": 0.102}, {"sql": "... 331 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"App\\Models\\Registration": {"value": 424, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\TotalScore": {"value": 392, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FTotalScore.php&line=1", "ajax": false, "filename": "TotalScore.php", "line": "?"}}, "App\\Models\\Course": {"value": 168, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\Department": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\Grade": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGrade.php&line=1", "ajax": false, "filename": "Grade.php", "line": "?"}}, "App\\Models\\User": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Semester": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}}, "count": 1032, "is_counter": true}, "livewire": {"data": {"app.filament.staff.resources.overview-resource.pages.manage-overview #6DVEjdyzxmBUHPiAWWP9": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"2\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"2\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.staff.resources.overview-resource.pages.manage-overview\"\n  \"component\" => \"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\"\n  \"id\" => \"6DVEjdyzxmBUHPiAWWP9\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview@loadTable<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/CanDeferLoading.php:17-20</a>", "middleware": "web", "duration": "3.49s", "peak_memory": "14MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2059487544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2059487544\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1240527051 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QYu2Lw1CCnONmOMgGdTSjAtKa8JeSmrzcF1GC2yX</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1883 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;2&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;2&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;6DVEjdyzxmBUHPiAWWP9&quot;,&quot;name&quot;:&quot;app.filament.staff.resources.overview-resource.pages.manage-overview&quot;,&quot;path&quot;:&quot;staff\\/overviews&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;5961346b5272a0a9a27d03a2304f0edeab4d113731f22486ea435891eb6d8054&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">loadTable</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240527051\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-618311319 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImtVU3dpWnFDRFNWM1d3RC94eEY5QXc9PSIsInZhbHVlIjoiZjhCa00yYitZWWdmZVBEaWNPelEyVTR6MzFUTlhQZ3VacGFDNS9TOUU0QTJxbzJvSmtuRnB5WmYvTk5HS0RtZDF3K1VtOTYrL0RGZENaRkJpT2lScWkyT25lSGxaMFNWMzJIQmtwWDB1K0hkSlZ4NitjckZybEZrbnpZSG9DMHAxTlo1eno2VVZXenJGdWVDZDVuUlhYUG05WU9QVEV2UkxTbU1YWllGc0czWTB3MGt1QW9Ha0tCMG8xZXRHbGZCVUEvOHdwMzlKYjhkNXV2THFsdnVXWjF0K25JN0dXdnF4Ym5JQkJZY1ZUQT0iLCJtYWMiOiJlOGIwOWFiY2ZkNjRiNTM3YThlY2RlMjU3MzA0NWFlODdiZGZhY2Q5MzBmYzE2OWZhNjdlMGU5MjhhMjdkMjI5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjczVjZsaHJQVWNYRG0xY3RiRWxkelE9PSIsInZhbHVlIjoiYlAxSkt0Z1JMRm5nMXhLS2I4WXdwc0RLQmMxTE1jRGVaV291ZkJMUHdXUTdLQ01NR2tURDBBSS8rQ0xWZklhTUM2U3M1M2NjMWNuNFRPTXI3UmlxTWRRZUhadG5pM09jbTZkNjZnQjduSXVXTy9PaVFzYmkrMmVua28wK0UwSloiLCJtYWMiOiI5ZjIxMWNjZjc5OTEzMmY0YTBkOTg1MjQzMTY4MTY3Zjk2ODA0YjA2NGJmYWU1YTAwMjFhNTJjZmI4MzE4YmRjIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6InZTWG1kM25DcjY2dE42b3EvMytqZnc9PSIsInZhbHVlIjoiaGJua1huSzFJWW52dUZkelhIeXhORXl3bVdnbUlRQlliVy9MYnZodk9FbXZsdnIvNjU1ZjV0UzBLdnFyZWlpNkZvYzVRaXBMVGxTVUpQbDJ6emdiUjQwTzExMmVHd0VMS0diU2pMeVMyRXZiSXBGaGtXQlEzUm5YYTZxZlRWdGEiLCJtYWMiOiJiOWM0YzkzYjRmZDg1Y2U0OTdjMGZjMWU0NzYxZDU5NjIxMWI2NjMxNDMyYTViMGQ2MjdmYTkwOTMzMTY3ZTIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"228 characters\">https://portal.racoed.test/staff/overviews?tableFilters[overview_filter][school_session_id]=3&amp;tableFilters[overview_filter][semester_id]=1&amp;tableFilters[overview_filter][level_id]=2&amp;tableFilters[overview_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Google Chrome&quot;;v=&quot;141&quot;, &quot;Not?A_Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;141&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/141.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2276</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618311319\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1478629208 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1H5vZ3kUjepNqOWpqLfZbHhO0p3Hpb1rxDVxB6uHj7v9NbxaMJIfpkgPgReu|$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QYu2Lw1CCnONmOMgGdTSjAtKa8JeSmrzcF1GC2yX</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xd1Tny3ZEeEN1t1yUHLLVdclXpUXxe7Qd1vuAoXN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478629208\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-146342593 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 09 Oct 2025 18:55:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146342593\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-448605576 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QYu2Lw1CCnONmOMgGdTSjAtKa8JeSmrzcF1GC2yX</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"260 characters\">https://portal.racoed.test/staff/overviews?tableFilters%5Boverview_filter%5D%5Bschool_session_id%5D=3&amp;tableFilters%5Boverview_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Boverview_filter%5D%5Blevel_id%5D=2&amp;tableFilters%5Boverview_filter%5D%5Bdepartment_id%5D=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448605576\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}
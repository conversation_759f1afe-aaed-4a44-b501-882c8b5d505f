<?php

namespace App\Filament\Staff\Resources\OverviewResource\Pages;

use App\Filament\Staff\Resources\OverviewResource;
use App\Models\Course;
use App\Models\Grade;
use Filament\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\ActionGroup;

class ManageOverview extends ManageRecords
{
    protected static string $resource = OverviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActionGroup::make([
                Action::make('view')
                    ->disabled(fn () => $this->hasNoRequiredFiltersNorFilteredRecords())
                    ->icon('heroicon-o-eye')
                    ->modalHeading('Overview')
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close')
                    ->modalWidth(MaxWidth::SixExtraLarge)
                    ->modalContent(function () {
                        $filters = $this->extractFilters();

                        $filteredRecords = $this->getFilteredTableQuery()->get();
                        $courses = Course::query()
                            ->where('department_id', $filters['department_id'])
                            ->where('level_id', $filters['level_id'])
                            ->where('semester_id', $filters['semester_id'])
                            ->get();

                        $grades = Grade::orderBy('min_score')->get();
                        $failedScore = Grade::where('min_score', 0)->value('max_score') ?? 39;

                        $semesterTotalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($courses);
                        $sampleRecord = $filteredRecords->first();
                        $cumulativeTotalCreditUnit = $sampleRecord
                            ? OverviewResource::getCumulativeTotalCreditUnit($this, $sampleRecord)
                            : 0;

                        return view('filament.pages.overview', [
                            'filters' => $filters,
                            'filteredRecords' => $filteredRecords,
                            'courses' => $courses,
                            'grades' => $grades,
                            'failedScore' => $failedScore,
                            'semesterTotalCreditUnit' => $semesterTotalCreditUnit,
                            'cumulativeTotalCreditUnit' => $cumulativeTotalCreditUnit,
                        ]);
                    }),
                    
            ])
                ->button()
                ->label(fn () => $this->hasNoRequiredFiltersNorFilteredRecords() ? 'Cannot export: view overview first' : 'Export overview')
                ->icon('heroicon-m-document-text'),
        ];
    }

    private function hasNoRequiredFiltersNorFilteredRecords(): bool
    {

        if (! $this->hasRequiredFilters()) {
            return true;
        }

        return $this->getFilteredRecords()->isEmpty();
    }

    private function getFilteredRecords()
    {
        return $this->getFilteredTableQuery()->get();
    }

    private function hasRequiredFilters(): bool
    {
        return ! in_array(null, $this->extractFilters(), true);
    }

    private function extractFilters(): array
    {
        $filters = $this->tableFilters['overview_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'department_id' => $filters['department_id'] ?? null,
        ];
    }
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="<?php echo e(asset('images/racoed-favicon.png')); ?>" type="image/png">
    <title>Acceptance letter - <?php echo e($student->name); ?></title>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <?php echo \Filament\Support\Facades\FilamentAsset::renderStyles() ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/filament/custom/theme.css', 'resources/css/app.css']); ?>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0mm;
            }

            .print\:hidden {
                display: none !important;
            }

            body {
                font-size: 10px;
            }

            h1 {
                font-size: 16px !important;
            }

            th,
            td {
                font-size: 10px !important;
            }

            * {
                border-color: #6b7280 !important;
                /* gray-500 */
                border-radius: 2px !important;
                /* small radius */
            }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        
        <?php if (isset($component)) { $__componentOriginal748b58a65cd16e6aeabed19711d43de4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal748b58a65cd16e6aeabed19711d43de4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.document-header','data' => ['collegeLogo' => asset('images/racoed-favicon.png'),'collegeName' => $collegeSettings->name,'collegeMotto' => $collegeSettings->motto,'collegeAddress' => $collegeSettings->address,'collegePhone' => $collegeSettings->phone,'collegeEmail' => $collegeSettings->email,'studentPhoto' => $student->photo ? Storage::url($student->photo) : asset('images/placeholder.png'),'heading' => 'Academic Affairs & Support','subheading' => 'Acceptance Letter']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('document-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['collegeLogo' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(asset('images/racoed-favicon.png')),'collegeName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->name),'collegeMotto' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->motto),'collegeAddress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->address),'collegePhone' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->phone),'collegeEmail' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->email),'studentPhoto' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')),'heading' => 'Academic Affairs & Support','subheading' => 'Acceptance Letter']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal748b58a65cd16e6aeabed19711d43de4)): ?>
<?php $attributes = $__attributesOriginal748b58a65cd16e6aeabed19711d43de4; ?>
<?php unset($__attributesOriginal748b58a65cd16e6aeabed19711d43de4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal748b58a65cd16e6aeabed19711d43de4)): ?>
<?php $component = $__componentOriginal748b58a65cd16e6aeabed19711d43de4; ?>
<?php unset($__componentOriginal748b58a65cd16e6aeabed19711d43de4); ?>
<?php endif; ?>

        
        <div>
            <p class="leading-relaxed"><b><?php echo e($student->name ?? 'NIL'); ?></b></p>
            <p class="leading-relaxed"><?php echo e($student->address ?? 'NIL'); ?></p>
            <i><?php echo e($student?->application?->admission_date?->format('d M, Y') ?? 'NIL'); ?></i>
        </div>

        
        <div class="flex items-center justify-center">
            <h1 class="font-bold">LETTER OF ACCEPTANCE</h1>
        </div>

        
        <div>
            <p class="leading-relaxed text-base mb-4">To: The Registrar,</p>

            <p class="leading-relaxed mb-4">
                I, <b><?php echo e($student->name ?? 'NIL'); ?></b>, hereby accept the offer of provisional admission into <b><?php echo e($collegeSettings->name ?? 'NIL'); ?></b>, under the <b>Alhaji Amusa Wahabi Omotosho Foundation
                    Tuition-Free Scholarship Scheme</b>.
            </p>

            <p class="leading-relaxed mb-4">
                I understand that the offer is to study <b><?php echo e($student->application?->programme->name ?? 'NIL'); ?></b>
                (Full-Time), a six-semester programme leading to the award of the <b>Nigeria Certificate in Education
                    (NCE)</b> for the <b><?php echo e($student->application->schoolSession->name ?? 'NIL'); ?></b> academic session.
            </p>

            <p class="leading-relaxed mb-4">
                I fully understand and accept all the conditions attached to this admission, including but not limited
                to: payment of all applicable fees, submission of valid documents, and compliance with all academic and
                institutional requirements.
            </p>

            <p class="leading-relaxed mb-4">
                I pledge to abide by the rules and regulations of the college throughout my period of study.
            </p>

            <p class="leading-relaxed mb-4">Thank you.</p>

            <p class="leading-relaxed mt-6 mb-1"><b>Student's Signature:</b></p>
            <hr class="border-t border-gray-700 w-48">

            <p class="mt-4 mb-1"><b>Date:</b></p>
            <hr class="border-t border-gray-700 w-48">
        </div>

        
        <div class="fixed bottom-4 right-4 print:hidden">
            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['tag' => 'button','color' => 'primary','icon' => 'heroicon-o-printer','onclick' => 'window.print()']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'button','color' => 'primary','icon' => 'heroicon-o-printer','onclick' => 'window.print()']); ?>
                Print acceptance letter
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
        </div>

        <script>
            // Automatically open print dialog when the page loads
        window.onload = function() {
            window.print();
        }
        </script>
    </div>

</body>

</html><?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/documents/acceptance-letter.blade.php ENDPATH**/ ?>
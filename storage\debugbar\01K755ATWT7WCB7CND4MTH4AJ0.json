{"__meta": {"id": "01K755ATWT7WCB7CND4MTH4AJ0", "datetime": "2025-10-09 19:56:59", "utime": **********.80348, "method": "GET", "uri": "/staff/overviews?tableFilters[overview_filter][school_session_id]=3&tableFilters[overview_filter][semester_id]=1&tableFilters[overview_filter][level_id]=2&tableFilters[overview_filter][department_id]=16", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[19:56:59] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.365553, "xdebug_link": null, "collector": "log"}, {"message": "[19:56:59] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.366035, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1760036218.665915, "end": **********.803532, "duration": 1.1376168727874756, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1760036218.665915, "relative_start": 0, "end": **********.091764, "relative_end": **********.091764, "duration": 0.*****************, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.091784, "relative_start": 0.****************, "end": **********.803537, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "712ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.103333, "relative_start": 0.*****************, "end": **********.105659, "relative_end": **********.105659, "duration": 0.0023260116577148438, "duration_str": "2.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.292615, "relative_start": 0.****************, "end": **********.292615, "relative_end": **********.292615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.309537, "relative_start": 0.****************, "end": **********.309537, "relative_end": **********.309537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.320796, "relative_start": 0.6548810005187988, "end": **********.320796, "relative_end": **********.320796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.332022, "relative_start": 0.6661069393157959, "end": **********.332022, "relative_end": **********.332022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.340336, "relative_start": 0.6744210720062256, "end": **********.340336, "relative_end": **********.340336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.361059, "relative_start": 0.6951439380645752, "end": **********.361059, "relative_end": **********.361059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.361989, "relative_start": 0.6960740089416504, "end": **********.361989, "relative_end": **********.361989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.373986, "relative_start": 0.7080709934234619, "end": **********.373986, "relative_end": **********.373986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.399449, "relative_start": 0.7335340976715088, "end": **********.399449, "relative_end": **********.399449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.school-details", "start": **********.485465, "relative_start": 0.8195500373840332, "end": **********.485465, "relative_end": **********.485465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.555142, "relative_start": 0.8892269134521484, "end": **********.555142, "relative_end": **********.555142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.welcome-user", "start": **********.559002, "relative_start": 0.8930869102478027, "end": **********.559002, "relative_end": **********.559002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fd1940d56c351ec3a267a57ee9c54a44", "start": **********.576937, "relative_start": 0.9110219478607178, "end": **********.576937, "relative_end": **********.576937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-knowledge-base::livewire.help-menu", "start": **********.582076, "relative_start": 0.916161060333252, "end": **********.582076, "relative_end": **********.582076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-easy-footer::easy-footer", "start": **********.588271, "relative_start": 0.9223558902740479, "end": **********.588271, "relative_end": **********.588271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-easy-footer::github-version", "start": **********.595909, "relative_start": 0.9299941062927246, "end": **********.595909, "relative_end": **********.595909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.analytics-tag", "start": **********.731727, "relative_start": 1.0658118724822998, "end": **********.731727, "relative_end": **********.731727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f0ee69f5395275435701a1ff333f6962", "start": **********.743015, "relative_start": 1.0771000385284424, "end": **********.743015, "relative_end": **********.743015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-knowledge-base::livewire.modals", "start": **********.746816, "relative_start": 1.0809009075164795, "end": **********.746816, "relative_end": **********.746816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.750234, "relative_start": 1.0843188762664795, "end": **********.750396, "relative_end": **********.750396, "duration": 0.0001621246337890625, "duration_str": "162μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.798214, "relative_start": 1.1322989463806152, "end": **********.79856, "relative_end": **********.79856, "duration": 0.0003459453582763672, "duration_str": "346μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7451152, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 19, "nb_templates": 19, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.292526, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.309453, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.320711, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.33194, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.340252, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.360976, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.361902, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.373868, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.399364, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament.hooks.school-details", "param_count": null, "params": [], "start": **********.48538, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.phpfilament.hooks.school-details", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fschool-details.blade.php&line=1", "ajax": false, "filename": "school-details.blade.php", "line": "?"}}, {"name": "__components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.555058, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}}, {"name": "filament.hooks.welcome-user", "param_count": null, "params": [], "start": **********.558923, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/welcome-user.blade.phpfilament.hooks.welcome-user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fwelcome-user.blade.php&line=1", "ajax": false, "filename": "welcome-user.blade.php", "line": "?"}}, {"name": "__components::fd1940d56c351ec3a267a57ee9c54a44", "param_count": null, "params": [], "start": **********.576855, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/fd1940d56c351ec3a267a57ee9c54a44.blade.php__components::fd1940d56c351ec3a267a57ee9c54a44", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Ffd1940d56c351ec3a267a57ee9c54a44.blade.php&line=1", "ajax": false, "filename": "fd1940d56c351ec3a267a57ee9c54a44.blade.php", "line": "?"}}, {"name": "filament-knowledge-base::livewire.help-menu", "param_count": null, "params": [], "start": **********.581866, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\guava\\filament-knowledge-base\\src\\/../resources/views/livewire/help-menu.blade.phpfilament-knowledge-base::livewire.help-menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fguava%2Ffilament-knowledge-base%2Fresources%2Fviews%2Flivewire%2Fhelp-menu.blade.php&line=1", "ajax": false, "filename": "help-menu.blade.php", "line": "?"}}, {"name": "filament-easy-footer::easy-footer", "param_count": null, "params": [], "start": **********.588184, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\devonab\\filament-easy-footer\\src\\/../resources/views/easy-footer.blade.phpfilament-easy-footer::easy-footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fdevonab%2Ffilament-easy-footer%2Fresources%2Fviews%2Feasy-footer.blade.php&line=1", "ajax": false, "filename": "easy-footer.blade.php", "line": "?"}}, {"name": "filament-easy-footer::github-version", "param_count": null, "params": [], "start": **********.595821, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\devonab\\filament-easy-footer\\src\\/../resources/views/github-version.blade.phpfilament-easy-footer::github-version", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fdevonab%2Ffilament-easy-footer%2Fresources%2Fviews%2Fgithub-version.blade.php&line=1", "ajax": false, "filename": "github-version.blade.php", "line": "?"}}, {"name": "filament.hooks.analytics-tag", "param_count": null, "params": [], "start": **********.731644, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/analytics-tag.blade.phpfilament.hooks.analytics-tag", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fanalytics-tag.blade.php&line=1", "ajax": false, "filename": "analytics-tag.blade.php", "line": "?"}}, {"name": "__components::f0ee69f5395275435701a1ff333f6962", "param_count": null, "params": [], "start": **********.742931, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/f0ee69f5395275435701a1ff333f6962.blade.php__components::f0ee69f5395275435701a1ff333f6962", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Ff0ee69f5395275435701a1ff333f6962.blade.php&line=1", "ajax": false, "filename": "f0ee69f5395275435701a1ff333f6962.blade.php", "line": "?"}}, {"name": "filament-knowledge-base::livewire.modals", "param_count": null, "params": [], "start": **********.746726, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\guava\\filament-knowledge-base\\src\\/../resources/views/livewire/modals.blade.phpfilament-knowledge-base::livewire.modals", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fguava%2Ffilament-knowledge-base%2Fresources%2Fviews%2Flivewire%2Fmodals.blade.php&line=1", "ajax": false, "filename": "modals.blade.php", "line": "?"}}]}, "queries": {"count": 52, "nb_statements": 51, "nb_visible_statements": 52, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10039999999999999, "accumulated_duration_str": "100ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.120864, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'xd1Tny3ZEeEN1t1yUHLLVdclXpUXxe7Qd1vuAoXN' limit 1", "type": "query", "params": [], "bindings": ["xd1Tny3ZEeEN1t1yUHLLVdclXpUXxe7Qd1vuAoXN"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.1229181, "duration": 0.014369999999999999, "duration_str": "14.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 14.313}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.1470401, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 14.313, "width_percent": 0.996}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.1925411, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 15.309, "width_percent": 0.817}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.1996698, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 16.125, "width_percent": 0.747}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-10-09' and date(`semester_end`) >= '2025-10-09' limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-09", "2025-10-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.205702, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 16.873, "width_percent": 1.006}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '3' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 193}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.239853, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:193", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=193", "ajax": false, "filename": "OverviewResource.php", "line": "193"}, "connection": "racoed", "explain": null, "start_percent": 17.878, "width_percent": 0.886}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 199}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.247298, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:199", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=199", "ajax": false, "filename": "OverviewResource.php", "line": "199"}, "connection": "racoed", "explain": null, "start_percent": 18.765, "width_percent": 0.757}, {"sql": "select * from `levels` where `levels`.`id` = '2' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 205}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.251998, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:205", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 205}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=205", "ajax": false, "filename": "OverviewResource.php", "line": "205"}, "connection": "racoed", "explain": null, "start_percent": 19.522, "width_percent": 0.876}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 211}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.256212, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:211", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=211", "ajax": false, "filename": "OverviewResource.php", "line": "211"}, "connection": "racoed", "explain": null, "start_percent": 20.398, "width_percent": 0.677}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 130}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.2843692, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:130", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=130", "ajax": false, "filename": "OverviewResource.php", "line": "130"}, "connection": "racoed", "explain": null, "start_percent": 21.076, "width_percent": 0.966}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 140}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.3028011, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:140", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=140", "ajax": false, "filename": "OverviewResource.php", "line": "140"}, "connection": "racoed", "explain": null, "start_percent": 22.042, "width_percent": 1.026}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 150}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.314144, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:150", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=150", "ajax": false, "filename": "OverviewResource.php", "line": "150"}, "connection": "racoed", "explain": null, "start_percent": 23.068, "width_percent": 0.757}, {"sql": "select `name`, `id` from `departments` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 160}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.325316, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:160", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=160", "ajax": false, "filename": "OverviewResource.php", "line": "160"}, "connection": "racoed", "explain": null, "start_percent": 23.825, "width_percent": 0.767}, {"sql": "select count(*) as aggregate from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/AssessmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\AssessmentResource.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.4063032, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AssessmentResource.php:55", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/AssessmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\AssessmentResource.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FAssessmentResource.php&line=55", "ajax": false, "filename": "AssessmentResource.php", "line": "55"}, "connection": "racoed", "explain": null, "start_percent": 24.592, "width_percent": 0.657}, {"sql": "select count(*) as aggregate from `courses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/CourseResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\CourseResource.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.412792, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "CourseResource.php:56", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/CourseResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\CourseResource.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FCourseResource.php&line=56", "ajax": false, "filename": "CourseResource.php", "line": "56"}, "connection": "racoed", "explain": null, "start_percent": 25.249, "width_percent": 0.727}, {"sql": "select count(*) as aggregate from `departments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/DepartmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\DepartmentResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.417856, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DepartmentResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/DepartmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\DepartmentResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FDepartmentResource.php&line=51", "ajax": false, "filename": "DepartmentResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 25.976, "width_percent": 0.906}, {"sql": "select count(*) as aggregate from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/GradeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\GradeResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.423183, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "GradeResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/GradeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\GradeResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FGradeResource.php&line=51", "ajax": false, "filename": "GradeResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 26.882, "width_percent": 1.803}, {"sql": "select count(*) as aggregate from `invoices` where `invoice_status` = 3 and ((`invoices`.`payable_type` = 'App\\\\Models\\\\Fee' and exists (select * from `fees` where `invoices`.`payable_id` = `fees`.`id`)) or (`invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and exists (select * from `registrations` where `invoices`.`payable_id` = `registrations`.`id`))) and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3, "App\\Models\\Fee", "App\\Models\\Registration"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\InvoiceResource.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.432185, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "InvoiceResource.php:48", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\InvoiceResource.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FInvoiceResource.php&line=48", "ajax": false, "filename": "InvoiceResource.php", "line": "48"}, "connection": "racoed", "explain": null, "start_percent": 28.685, "width_percent": 1.773}, {"sql": "select count(*) as aggregate from `levels` where `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/LevelResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\LevelResource.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.438408, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "LevelResource.php:50", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/LevelResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\LevelResource.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FLevelResource.php&line=50", "ajax": false, "filename": "LevelResource.php", "line": "50"}, "connection": "racoed", "explain": null, "start_percent": 30.458, "width_percent": 0.876}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/NewsResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\NewsResource.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.445509, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "NewsResource.php:62", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/NewsResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\NewsResource.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FNewsResource.php&line=62", "ajax": false, "filename": "NewsResource.php", "line": "62"}, "connection": "racoed", "explain": null, "start_percent": 31.335, "width_percent": 0.787}, {"sql": "select count(*) as aggregate from `programmes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/ProgrammeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\ProgrammeResource.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.452479, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ProgrammeResource.php:54", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/ProgrammeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\ProgrammeResource.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FProgrammeResource.php&line=54", "ajax": false, "filename": "ProgrammeResource.php", "line": "54"}, "connection": "racoed", "explain": null, "start_percent": 32.122, "width_percent": 0.697}, {"sql": "select count(*) as aggregate from `schools`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.457477, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "SchoolResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSchoolResource.php&line=49", "ajax": false, "filename": "SchoolResource.php", "line": "49"}, "connection": "racoed", "explain": null, "start_percent": 32.819, "width_percent": 1.853}, {"sql": "select count(*) as aggregate from `school_sessions` where `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolSessionResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolSessionResource.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.462713, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SchoolSessionResource.php:59", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolSessionResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolSessionResource.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSchoolSessionResource.php&line=59", "ajax": false, "filename": "SchoolSessionResource.php", "line": "59"}, "connection": "racoed", "explain": null, "start_percent": 34.671, "width_percent": 0.637}, {"sql": "select count(*) as aggregate from `semesters` where `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SemesterResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SemesterResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.4672852, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "SemesterResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SemesterResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SemesterResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSemesterResource.php&line=49", "ajax": false, "filename": "SemesterResource.php", "line": "49"}, "connection": "racoed", "explain": null, "start_percent": 35.309, "width_percent": 0.438}, {"sql": "select count(*) as aggregate from `users` where `users`.`role` != 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StaffResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StaffResource.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.472713, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "StaffResource.php:41", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StaffResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StaffResource.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStaffResource.php&line=41", "ajax": false, "filename": "StaffResource.php", "line": "41"}, "connection": "racoed", "explain": null, "start_percent": 35.747, "width_percent": 0.837}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 104}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.47826, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "StudentResource.php:104", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=104", "ajax": false, "filename": "StudentResource.php", "line": "104"}, "connection": "racoed", "explain": null, "start_percent": 36.584, "width_percent": 1.145}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 8}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.4870222, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 37.729, "width_percent": 0.966}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.493115, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 38.695, "width_percent": 0.757}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 order by `semester_start` asc", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 149}, {"index": 16, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.498252, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:149", "source": {"index": 15, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 149}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=149", "ajax": false, "filename": "AcademicCalendarService.php", "line": "149"}, "connection": "racoed", "explain": null, "start_percent": 39.452, "width_percent": 1.026}, {"sql": "select * from `semesters` where `semesters`.`id` in (1, 2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 149}, {"index": 21, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.504026, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:149", "source": {"index": 20, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 149}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=149", "ajax": false, "filename": "AcademicCalendarService.php", "line": "149"}, "connection": "racoed", "explain": null, "start_percent": 40.478, "width_percent": 0.916}, {"sql": "select `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_end` desc limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, {"index": 17, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 178}, {"index": 18, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.508446, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:206", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=206", "ajax": false, "filename": "AcademicCalendarService.php", "line": "206"}, "connection": "racoed", "explain": null, "start_percent": 41.394, "width_percent": 0.827}, {"sql": "select * from `school_sessions` where exists (select * from `semester_schedules` where `school_sessions`.`id` = `semester_schedules`.`school_session_id` and `semester_start` > '2025-08-30 23:59:59') and `school_sessions`.`deleted_at` is null order by `id` asc limit 1", "type": "query", "params": [], "bindings": ["2025-08-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 183}, {"index": 17, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.5145712, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:183", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=183", "ajax": false, "filename": "AcademicCalendarService.php", "line": "183"}, "connection": "racoed", "explain": null, "start_percent": 42.221, "width_percent": 1.345}, {"sql": "select * from `semester_schedules` where `school_session_id` = 4 order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 189}, {"index": 17, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.5199308, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:189", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=189", "ajax": false, "filename": "AcademicCalendarService.php", "line": "189"}, "connection": "racoed", "explain": null, "start_percent": 43.566, "width_percent": 0.956}, {"sql": "select * from `semesters` where `semesters`.`id` in (1) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 189}, {"index": 22, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.525339, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:189", "source": {"index": 21, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=189", "ajax": false, "filename": "AcademicCalendarService.php", "line": "189"}, "connection": "racoed", "explain": null, "start_percent": 44.522, "width_percent": 0.707}, {"sql": "select * from `cache` where `key` in ('settings_defaults')", "type": "query", "params": [], "bindings": ["settings_defaults"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.534486, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "racoed", "explain": null, "start_percent": 45.229, "width_percent": 0.906}, {"sql": "select `name`, `payload` from `settings` where `group` = 'college'", "type": "query", "params": [], "bindings": ["college"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "app/Settings/MultiTenantSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Settings\\MultiTenantSettingsRepository.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 89}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 277}], "start": **********.540338, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:28", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=28", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 46.135, "width_percent": 0.807}, {"sql": "select * from `cache` where `key` in ('filament-easy-footer.github.mikailhamzat/racoed-cportal.latest-tag')", "type": "query", "params": [], "bindings": ["filament-easy-footer.github.mikailhamzat/racoed-cportal.latest-tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/devonab/filament-easy-footer/src/Services/GitHubService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\devonab\\filament-easy-footer\\src\\Services\\GitHubService.php", "line": 115}, {"index": 17, "namespace": null, "name": "vendor/devonab/filament-easy-footer/src/Services/GitHubService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\devonab\\filament-easy-footer\\src\\Services\\GitHubService.php", "line": 76}], "start": **********.590154, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "racoed", "explain": null, "start_percent": 46.942, "width_percent": 0.717}, {"sql": "select count(*) as aggregate from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/AssessmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\AssessmentResource.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.6003501, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AssessmentResource.php:55", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/AssessmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\AssessmentResource.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FAssessmentResource.php&line=55", "ajax": false, "filename": "AssessmentResource.php", "line": "55"}, "connection": "racoed", "explain": null, "start_percent": 47.659, "width_percent": 0.657}, {"sql": "select count(*) as aggregate from `courses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/CourseResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\CourseResource.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.6051638, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "CourseResource.php:56", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/CourseResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\CourseResource.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FCourseResource.php&line=56", "ajax": false, "filename": "CourseResource.php", "line": "56"}, "connection": "racoed", "explain": null, "start_percent": 48.317, "width_percent": 0.757}, {"sql": "select count(*) as aggregate from `departments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/DepartmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\DepartmentResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.609356, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DepartmentResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/DepartmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\DepartmentResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FDepartmentResource.php&line=51", "ajax": false, "filename": "DepartmentResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 49.074, "width_percent": 0.548}, {"sql": "select count(*) as aggregate from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/GradeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\GradeResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.614891, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "GradeResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/GradeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\GradeResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FGradeResource.php&line=51", "ajax": false, "filename": "GradeResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 49.622, "width_percent": 1.902}, {"sql": "select count(*) as aggregate from `invoices` where `invoice_status` = 3 and ((`invoices`.`payable_type` = 'App\\\\Models\\\\Fee' and exists (select * from `fees` where `invoices`.`payable_id` = `fees`.`id`)) or (`invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and exists (select * from `registrations` where `invoices`.`payable_id` = `registrations`.`id`))) and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3, "App\\Models\\Fee", "App\\Models\\Registration"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\InvoiceResource.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.621608, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "InvoiceResource.php:48", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\InvoiceResource.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FInvoiceResource.php&line=48", "ajax": false, "filename": "InvoiceResource.php", "line": "48"}, "connection": "racoed", "explain": null, "start_percent": 51.524, "width_percent": 1.743}, {"sql": "select count(*) as aggregate from `levels` where `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/LevelResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\LevelResource.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.6269858, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "LevelResource.php:50", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/LevelResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\LevelResource.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FLevelResource.php&line=50", "ajax": false, "filename": "LevelResource.php", "line": "50"}, "connection": "racoed", "explain": null, "start_percent": 53.267, "width_percent": 0.608}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/NewsResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\NewsResource.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.630961, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "NewsResource.php:62", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/NewsResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\NewsResource.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FNewsResource.php&line=62", "ajax": false, "filename": "NewsResource.php", "line": "62"}, "connection": "racoed", "explain": null, "start_percent": 53.875, "width_percent": 0.508}, {"sql": "select count(*) as aggregate from `programmes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/ProgrammeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\ProgrammeResource.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.635633, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ProgrammeResource.php:54", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/ProgrammeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\ProgrammeResource.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FProgrammeResource.php&line=54", "ajax": false, "filename": "ProgrammeResource.php", "line": "54"}, "connection": "racoed", "explain": null, "start_percent": 54.382, "width_percent": 0.667}, {"sql": "select count(*) as aggregate from `schools`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.639869, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "SchoolResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSchoolResource.php&line=49", "ajax": false, "filename": "SchoolResource.php", "line": "49"}, "connection": "racoed", "explain": null, "start_percent": 55.05, "width_percent": 1.763}, {"sql": "select count(*) as aggregate from `school_sessions` where `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolSessionResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolSessionResource.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.64715, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "SchoolSessionResource.php:59", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolSessionResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolSessionResource.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSchoolSessionResource.php&line=59", "ajax": false, "filename": "SchoolSessionResource.php", "line": "59"}, "connection": "racoed", "explain": null, "start_percent": 56.813, "width_percent": 1.066}, {"sql": "select count(*) as aggregate from `semesters` where `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SemesterResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SemesterResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.652111, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "SemesterResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SemesterResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SemesterResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSemesterResource.php&line=49", "ajax": false, "filename": "SemesterResource.php", "line": "49"}, "connection": "racoed", "explain": null, "start_percent": 57.878, "width_percent": 0.677}, {"sql": "select count(*) as aggregate from `users` where `users`.`role` != 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StaffResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StaffResource.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.656458, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "StaffResource.php:41", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StaffResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StaffResource.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStaffResource.php&line=41", "ajax": false, "filename": "StaffResource.php", "line": "41"}, "connection": "racoed", "explain": null, "start_percent": 58.556, "width_percent": 0.697}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 104}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.660866, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:104", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=104", "ajax": false, "filename": "StudentResource.php", "line": "104"}, "connection": "racoed", "explain": null, "start_percent": 59.253, "width_percent": 0.876}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiUVl1Mkx3MUNDbk9ObU9NZ0dkVFNqQXRLYThKZVNtcnpjRjFHQzJ5WCI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJDZKbGdVTW5wM3hnRElYY0JTVzl0Qk9ka2lNbDhjRkdwY1pwM21Sc0N4NE9zbWZEUVZZWU5HIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czoyNjA6Imh0dHBzOi8vcG9ydGFsLnJhY29lZC50ZXN0L3N0YWZmL292ZXJ2aWV3cz90YWJsZUZpbHRlcnMlNUJvdmVydmlld19maWx0ZXIlNUQlNUJzY2hvb2xfc2Vzc2lvbl9pZCU1RD0zJnRhYmxlRmlsdGVycyU1Qm92ZXJ2aWV3X2ZpbHRlciU1RCU1QnNlbWVzdGVyX2lkJTVEPTEmdGFibGVGaWx0ZXJzJTVCb3ZlcnZpZXdfZmlsdGVyJTVEJTVCbGV2ZWxfaWQlNUQ9MiZ0YWJsZUZpbHRlcnMlNUJvdmVydmlld19maWx0ZXIlNUQlNUJkZXBhcnRtZW50X2lkJTVEPTE2Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'xd1Tny3ZEeEN1t1yUHLLVdclXpUXxe7Qd1vuAoXN'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiUVl1Mkx3MUNDbk9ObU9NZ0dkVFNqQXRLYThKZVNtcnpjRjFHQzJ5WCI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJDZKbGdVTW5wM3hnRElYY0JTVzl0Qk9ka2lNbDhjRkdwY1pwM21Sc0N4NE9zbWZEUVZZWU5HIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czoyNjA6Imh0dHBzOi8vcG9ydGFsLnJhY29lZC50ZXN0L3N0YWZmL292ZXJ2aWV3cz90YWJsZUZpbHRlcnMlNUJvdmVydmlld19maWx0ZXIlNUQlNUJzY2hvb2xfc2Vzc2lvbl9pZCU1RD0zJnRhYmxlRmlsdGVycyU1Qm92ZXJ2aWV3X2ZpbHRlciU1RCU1QnNlbWVzdGVyX2lkJTVEPTEmdGFibGVGaWx0ZXJzJTVCb3ZlcnZpZXdfZmlsdGVyJTVEJTVCbGV2ZWxfaWQlNUQ9MiZ0YWJsZUZpbHRlcnMlNUJvdmVydmlld19maWx0ZXIlNUQlNUJkZXBhcnRtZW50X2lkJTVEPTE2Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "xd1Tny3ZEeEN1t1yUHLLVdclXpUXxe7Qd1vuAoXN"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.752512, "duration": 0.04003, "duration_str": "40.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "racoed", "explain": null, "start_percent": 60.129, "width_percent": 39.871}]}, "models": {"data": {"App\\Models\\Department": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "Spatie\\LaravelSettings\\Models\\SettingsProperty": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FModels%2FSettingsProperty.php&line=1", "ajax": false, "filename": "SettingsProperty.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Semester": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\SemesterSchedule": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemesterSchedule.php&line=1", "ajax": false, "filename": "SemesterSchedule.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}}, "count": 45, "is_counter": true}, "livewire": {"data": {"app.filament.staff.resources.overview-resource.pages.manage-overview #WgRfmXIRP7aNnNCUbqK9": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"2\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"2\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.staff.resources.overview-resource.pages.manage-overview\"\n  \"component\" => \"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\"\n  \"id\" => \"WgRfmXIRP7aNnNCUbqK9\"\n]", "help-menu #WBD2fzkBvKuTmBGNIQMa": "array:4 [\n  \"data\" => array:11 [\n    \"documentation\" => []\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n  ]\n  \"name\" => \"help-menu\"\n  \"component\" => \"Guava\\FilamentKnowledgeBase\\Livewire\\HelpMenu\"\n  \"id\" => \"WBD2fzkBvKuTmBGNIQMa\"\n]", "devonab.filament-easy-footer.github-version #A1OHPrvhmWQV3chAJaNO": "array:4 [\n  \"data\" => array:4 [\n    \"showLogo\" => false\n    \"showUrl\" => false\n    \"version\" => \"v1.2.0\"\n    \"repository\" => \"mikailhamzat/racoed-cportal\"\n  ]\n  \"name\" => \"devonab.filament-easy-footer.github-version\"\n  \"component\" => \"Devonab\\FilamentEasyFooter\\Livewire\\GitHubVersion\"\n  \"id\" => \"A1OHPrvhmWQV3chAJaNO\"\n]", "filament.livewire.notifications #OR4BeMnfDC21T8EJgRnm": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3772\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"OR4BeMnfDC21T8EJgRnm\"\n]", "modals #w6c6d2bLH8ZX31FXsvFi": "array:4 [\n  \"data\" => array:11 [\n    \"documentable\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n  ]\n  \"name\" => \"modals\"\n  \"component\" => \"Guava\\FilamentKnowledgeBase\\Livewire\\Modals\"\n  \"id\" => \"w6c6d2bLH8ZX31FXsvFi\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/staff/overviews?tableFilters%5Boverview_filter%5D%5Bschool_session_id%5D=...", "action_name": "filament.staff.resources.overviews.index", "controller_action": "App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview", "uri": "GET staff/overviews", "domain": "https://portal.racoed.test", "controller": "App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview@render<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "staff/overviews", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:staff, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "1.16s", "peak_memory": "12MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>tableFilters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>overview_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-491191270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-491191270\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1898783779 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImtVU3dpWnFDRFNWM1d3RC94eEY5QXc9PSIsInZhbHVlIjoiZjhCa00yYitZWWdmZVBEaWNPelEyVTR6MzFUTlhQZ3VacGFDNS9TOUU0QTJxbzJvSmtuRnB5WmYvTk5HS0RtZDF3K1VtOTYrL0RGZENaRkJpT2lScWkyT25lSGxaMFNWMzJIQmtwWDB1K0hkSlZ4NitjckZybEZrbnpZSG9DMHAxTlo1eno2VVZXenJGdWVDZDVuUlhYUG05WU9QVEV2UkxTbU1YWllGc0czWTB3MGt1QW9Ha0tCMG8xZXRHbGZCVUEvOHdwMzlKYjhkNXV2THFsdnVXWjF0K25JN0dXdnF4Ym5JQkJZY1ZUQT0iLCJtYWMiOiJlOGIwOWFiY2ZkNjRiNTM3YThlY2RlMjU3MzA0NWFlODdiZGZhY2Q5MzBmYzE2OWZhNjdlMGU5MjhhMjdkMjI5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjdLUWN3NEY2eks2aU9oY3duOXFhK0E9PSIsInZhbHVlIjoib25WV2xBQjhhZDRXQzAwMksrYlV6K2VManJ3T2o3QUUyUExtVXVoV0wwaE00d2dUVHRjZHJMYks4WFNzMGQ1RkYvdUJGNDcwVHpER2gwRjArMnpqcDU1MzB3b2s5QzlwbjU2VUZONHpjT2lWN0o1VEQ5OGpNR0pUV2RtamorUUQiLCJtYWMiOiJkZjY2MjI2NDM0ODg4YTY0YmE0Y2U5OTYyNGQyZTg4OTc1ZGFmMDEyMzBlNjljOGZmMjIwNmE3N2Q4NDNlMWYwIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6Ik9CN254bDBBS1plakdtR0x1cSsxWVE9PSIsInZhbHVlIjoieWVnREpUeS93a3FobGVLUjhkUEdYakRmVzdqSXVpWW9ubnVIckRLZHpXdlQrdVU2M0srRmJrZGNRd3VGUzRaVmpjV0hOS2JOOWpHYzJBMXpSTk5ndURZcC82Ymd1ZWRYYUZBQ3FYNHJ1U2lIVVlCSHQ3cGczR3NQWXBCYzg4Z3AiLCJtYWMiOiI5NzA4OGM0YjViMjBiYmVkZmQyNGYxNTNlYTU4OTEyM2Y5NjQ0NWQxN2UyNzdjNGY0ZmM0MzQzNjU5MDlmMTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"228 characters\">https://portal.racoed.test/staff/overviews?tableFilters[overview_filter][school_session_id]=3&amp;tableFilters[overview_filter][semester_id]=1&amp;tableFilters[overview_filter][level_id]=2&amp;tableFilters[overview_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Google Chrome&quot;;v=&quot;141&quot;, &quot;Not?A_Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;141&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898783779\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1607437525 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1H5vZ3kUjepNqOWpqLfZbHhO0p3Hpb1rxDVxB6uHj7v9NbxaMJIfpkgPgReu|$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QYu2Lw1CCnONmOMgGdTSjAtKa8JeSmrzcF1GC2yX</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xd1Tny3ZEeEN1t1yUHLLVdclXpUXxe7Qd1vuAoXN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607437525\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1256447339 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 09 Oct 2025 18:56:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"457 characters\">XSRF-TOKEN=eyJpdiI6ImlYSU5lclB6Mk9FUTZzRVZ4eFpuV1E9PSIsInZhbHVlIjoieUoveitXZDZHUVZDeTc5VGRMQUVIYklEVC8ydGFoMXlKdnJvOFMvMUJRMnA1YWpXd1N0SitYbUthbEVrS0h1RWJVMkVCRVB6VGdzdUJpVXlLa253YWcvS0g0ODl5Wk1iKzBwVWFuK1l6eXpGTzdha0djRi9Vbnl1ZGJqNDdxRmMiLCJtYWMiOiJjNDhmZDlhZTc2YTFhN2RkMjhhYjZlZjU2MmQ5OGRkYWNlNDJhOTVkYTYwN2E2M2VmMjRkYjU2ZTlhZTA3OWUwIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 20:56:59 GMT; Max-Age=7200; path=/; domain=.racoed.test; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"471 characters\">racoed_session=eyJpdiI6InlYb1JxdHlRQzhNT29WdXFlZ3l0a1E9PSIsInZhbHVlIjoiVWRubWYrUVN4RXZRSkJacnJudDh2SzJNMExVdWVyMThsR1VvQTJLZ0J5TVluOHN4eHpRb2p1WWVMSkh0TU1Qbnp4Qy9KOUFwNzBTRmQ5eC9Ick9EUnh2WFFkUzJJa0YyWWh1Y002cy8vT2xWdUh1cG9rOGE4eUdOWStFUHJQTTUiLCJtYWMiOiI2NTBiMjM4ODVhZDc0ZGFmNjRjMDhkNTUwMTgwZDQ0MTFkNTNhMDhjNThlNWI2OGVmNjc2Yzk0Y2Q1ZjFmZDYwIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 20:56:59 GMT; Max-Age=7200; path=/; domain=.racoed.test; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6ImlYSU5lclB6Mk9FUTZzRVZ4eFpuV1E9PSIsInZhbHVlIjoieUoveitXZDZHUVZDeTc5VGRMQUVIYklEVC8ydGFoMXlKdnJvOFMvMUJRMnA1YWpXd1N0SitYbUthbEVrS0h1RWJVMkVCRVB6VGdzdUJpVXlLa253YWcvS0g0ODl5Wk1iKzBwVWFuK1l6eXpGTzdha0djRi9Vbnl1ZGJqNDdxRmMiLCJtYWMiOiJjNDhmZDlhZTc2YTFhN2RkMjhhYjZlZjU2MmQ5OGRkYWNlNDJhOTVkYTYwN2E2M2VmMjRkYjU2ZTlhZTA3OWUwIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 20:56:59 GMT; domain=.racoed.test; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">racoed_session=eyJpdiI6InlYb1JxdHlRQzhNT29WdXFlZ3l0a1E9PSIsInZhbHVlIjoiVWRubWYrUVN4RXZRSkJacnJudDh2SzJNMExVdWVyMThsR1VvQTJLZ0J5TVluOHN4eHpRb2p1WWVMSkh0TU1Qbnp4Qy9KOUFwNzBTRmQ5eC9Ick9EUnh2WFFkUzJJa0YyWWh1Y002cy8vT2xWdUh1cG9rOGE4eUdOWStFUHJQTTUiLCJtYWMiOiI2NTBiMjM4ODVhZDc0ZGFmNjRjMDhkNTUwMTgwZDQ0MTFkNTNhMDhjNThlNWI2OGVmNjc2Yzk0Y2Q1ZjFmZDYwIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 20:56:59 GMT; domain=.racoed.test; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256447339\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1694258279 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QYu2Lw1CCnONmOMgGdTSjAtKa8JeSmrzcF1GC2yX</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"260 characters\">https://portal.racoed.test/staff/overviews?tableFilters%5Boverview_filter%5D%5Bschool_session_id%5D=3&amp;tableFilters%5Boverview_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Boverview_filter%5D%5Blevel_id%5D=2&amp;tableFilters%5Boverview_filter%5D%5Bdepartment_id%5D=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694258279\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/staff/overviews?tableFilters%5Boverview_filter%5D%5Bschool_session_id%5D=...", "action_name": "filament.staff.resources.overviews.index", "controller_action": "App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview"}, "badge": null}}